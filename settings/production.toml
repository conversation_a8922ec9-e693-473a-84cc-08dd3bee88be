# 生产环境配置
[production]
database_uri = "env_var_for_prod_db_uri"
log_level = "WARNING"
debug = false
secret_key = "prod-secret-key-change-this"
kuaidaili_dps_order_id = "your-prod-order-id-here"

# 数据库连接配置已移至 connections.toml
# 生产环境可以通过环境变量覆盖连接字符串
# 例如：APP_TORTOISE_ORM__CONNECTIONS__DEFAULT="mysql://..."

# 生产环境特定配置
app_title = "Vue FastAPI Admin"
project_name = "Vue FastAPI Admin"

# 生产环境严格的CORS设置
cors_origins = ["https://yourdomain.com", "https://www.yourdomain.com"]

# 生产环境JWT过期时间
jwt_access_token_expire_minutes = 1440  # 24 hours